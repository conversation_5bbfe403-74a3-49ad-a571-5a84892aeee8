import {
    Accordion,
    Accordion<PERSON>ontent,
    Accordion<PERSON><PERSON>,
    AccordionTrigger,
  } from "@/components/ui/accordion"
  
  const faqs = [
      {
        question: "What is AiFlix?",
        answer: "AiFlix is a pioneering platform showcasing short films that are conceptualized, and often fully generated, by advanced artificial intelligence systems. We explore the intersection of AI and cinematic arts."
      },
      {
        question: "Are the films really 100% AI-generated?",
        answer: "Our films range from AI-assisted to fully AI-generated. Some are based on a simple human prompt, while others are entirely conceived and visualized by AI. Each film's description provides details on the level of AI involvement."
      },
      {
        question: "What AI technologies are used?",
        answer: "We utilize a variety of cutting-edge AI models, including large language models (LLMs) for scriptwriting and storyboarding, and generative adversarial networks (GANs) or diffusion models for visual generation."
      },
      {
        question: "Can I submit my own AI-generated film?",
        answer: "We are not currently accepting public submissions, but we plan to open up a community platform in the future. Stay tuned for announcements!"
      },
      {
        question: "How often are new films added?",
        answer: "We aim to release new and experimental films on a regular basis. Follow us on our social media channels to get the latest updates on new drops."
      }
    ]
  
  export function Faq() {
    return (
      <section className="py-16 md:py-24 bg-background border-t border-border/40">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-headline font-bold">Frequently Asked Questions</h2>
              <p className="mt-4 max-w-2xl mx-auto text-lg text-muted-foreground">
                  Have questions? We have answers. If you can't find what you're looking for, feel free to contact us.
              </p>
          </div>
          <div className="max-w-3xl mx-auto">
              <Accordion type="single" collapsible className="w-full">
                  {faqs.map((faq, index) => (
                      <AccordionItem key={index} value={`item-${index + 1}`}>
                          <AccordionTrigger className="text-lg font-semibold text-left">{faq.question}</AccordionTrigger>
                          <AccordionContent className="text-base text-muted-foreground">
                              {faq.answer}
                          </AccordionContent>
                      </AccordionItem>
                  ))}
              </Accordion>
          </div>
        </div>
      </section>
    )
  }
  