import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import type { Film } from '@/lib/data';

interface FilmCardProps {
  film: Film;
}

export function FilmCard({ film }: FilmCardProps) {
  return (
    <Card className="flex flex-col overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-primary/20 hover:-translate-y-1.5 group">
      <div className="relative aspect-video overflow-hidden">
        <Image
          src={film.thumbnailUrl}
          alt={`Thumbnail for ${film.title}`}
          data-ai-hint={film.hint}
          fill
          className="object-cover transition-transform duration-500 ease-in-out group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
      </div>
      <CardHeader>
        <CardTitle className="font-headline text-xl line-clamp-1">{film.title}</CardTitle>
        <CardDescription className="line-clamp-3 h-[63px]">{film.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary">{film.genre}</Badge>
          {film.tags.map((tag) => (
            <Badge key={tag} variant="outline">
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button asChild className="w-full font-semibold">
          <Link href={film.videoUrl} target="_blank" rel="noopener noreferrer">
            Watch Now
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
