
export default function PrivacyPolicyPage() {
  return (
    <div className="container mx-auto px-4 py-16 lg:py-24">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-4xl md:text-5xl font-bold font-headline mb-6 text-center">Privacy Policy</h1>
        <div className="space-y-4 text-muted-foreground text-base">
          <p className="font-bold text-foreground">Last updated: July 22, 2024</p>

          <h2 className="text-2xl font-bold font-headline pt-6">1. Information We Collect</h2>
          <p>We do not collect any personal information from our users. Your privacy is important to us, and our platform is designed to be enjoyed without the need for you to share personal data.</p>
          
          <h2 className="text-2xl font-bold font-headline pt-6">2. Cookies</h2>
          <p>We may use cookies to enhance your experience. Cookies are small files stored on your device. We use them for functional purposes like remembering your preferences. We do not use cookies for tracking or advertising purposes.</p>

          <h2 className="text-2xl font-bold font-headline pt-6">3. Third-Party Services</h2>
          <p>Our website may contain links to other websites that are not operated by us. If you click on a third party link, you will be directed to that third party's site. We have no control over and assume no responsibility for the content, privacy policies or practices of any third party sites or services.</p>

          <h2 className="text-2xl font-bold font-headline pt-6">4. Changes to This Privacy Policy</h2>
          <p>We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page. You are advised to review this Privacy Policy periodically for any changes.</p>

          <h2 className="text-2xl font-bold font-headline pt-6">5. Contact Us</h2>
          <p>If you have any questions about this Privacy Policy, you can contact us at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>.</p>
        </div>
      </div>
    </div>
  );
}
