const fs = require("fs").promises;

async function convertRelativeDateToAbsolute() {
  const baseDate = "2025-07-09";
  const data = await fs.readFile("data/working.json", "utf8");
  const jsonArray = JSON.parse(data);

  // Parse the base date (July 9, 2025)
  const base = new Date(baseDate);

  // Helper function to parse relative date strings
  function parseRelativeDate(relativeStr) {
    const [amount, unit] = relativeStr.split(" ");
    const num = parseInt(amount);

    const date = new Date(base);
    if (unit.startsWith("day")) {
      date.setDate(base.getDate() - num);
    } else if (unit.startsWith("month")) {
      date.setMonth(base.getMonth() - num);
    } else if (unit.startsWith("year")) {
      date.setFullYear(base.getFullYear() - num);
    }

    // Format to YYYY-MM-DD
    return date.toISOString().split("T")[0];
  }

  // Traverse and update the array
  const updatedData = jsonArray.map((item) => ({
    ...item,
    publishedOn: item.publishedOn.includes("ago")
      ? parseRelativeDate(item.publishedOn)
      : item.publishedOn,
  }));
  await fs.writeFile("date.json", JSON.stringify(updatedData, null, 2));
}
convertRelativeDateToAbsolute();
