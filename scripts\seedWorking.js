import { config } from "dotenv";
import { neon } from "@neondatabase/serverless";
import { promises as fs } from "fs";

// Load environment variables
config();

// Initialize NeonDB connection

// Function to seed database
async function seedDatabase() {
  try {
    const sql = neon(process.env.DATABASE_URL);

    const data = await fs.readFile("data/final.json", "utf8");
    const movies = JSON.parse(data);
    // Begin transaction
    await sql`BEGIN`;

    console.log("movies", movies);

    // Seed movies
    for (const movie of movies) {
      await sql`
        INSERT INTO movies (title, tags, genre, src, "publishedOn")
        VALUES (${movie.title}, ${movie.tags}, ${movie.genre}, ${movie.src}, ${movie.publishedOn})
        ON CONFLICT (id) DO NOTHING
      `;
    }

    // Commit transaction
    await sql`COMMIT`;
    console.log("Database seeded successfully!");
  } catch (error) {
    // Rollback on error
    console.error("Error seeding database:", error);
  }
}

// Run the seed function and exit
seedDatabase()
  .then(() => {
    console.log("Seeding complete. Exiting...");
    process.exit(0);
  })
  .catch((err) => {
    console.error("Seed process failed:", err);
    process.exit(1);
  });
