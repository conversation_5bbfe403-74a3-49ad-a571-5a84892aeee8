import Link from 'next/link';
import { Clapperboard } from 'lucide-react';

export function Footer() {
  const year = new Date().getFullYear();
  return (
    <footer className="border-t border-border/40">
      <div className="container mx-auto max-w-7xl px-4 py-8">
        <div className="flex flex-col-reverse items-center gap-8 md:flex-row md:justify-between">
          <div className="text-center md:text-left text-sm text-muted-foreground">
            <div className="flex items-center justify-center md:justify-start gap-2 mb-2">
              <Clapperboard className="h-5 w-5" />
              <span className="font-headline font-semibold">AiFlix</span>
            </div>
            <p>&copy; {year} AiFlix. All Rights Reserved.</p>
          </div>
          <div className="flex flex-wrap justify-center gap-x-6 gap-y-2 text-sm">
            <Link href="/about" className="transition-colors hover:text-primary">About</Link>
            <Link href="/contact" className="transition-colors hover:text-primary">Contact</Link>
            <Link href="/terms" className="transition-colors hover:text-primary">Terms of Service</Link>
            <Link href="/privacy" className="transition-colors hover:text-primary">Privacy Policy</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
