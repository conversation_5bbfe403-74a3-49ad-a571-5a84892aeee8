export interface Film {
  id: string;
  title: string;
  thumbnailUrl: string;
  description: string;
  genre: string;
  tags: string[];
  videoUrl: string;
  hint: string;
}

export const films: Film[] = [
  {
    id: '1',
    title: 'Chrono-Echo',
    thumbnailUrl: 'https://placehold.co/600x400.png',
    description: 'A scientist discovers a way to receive echoes from the future, leading to a race against a predestined catastrophe.',
    genre: 'Sci-Fi',
    tags: ['Time Travel', 'Thriller'],
    videoUrl: '#',
    hint: 'time future',
  },
  {
    id: '2',
    title: 'Synthetic Dreams',
    thumbnailUrl: 'https://placehold.co/600x400.png',
    description: 'In a neon-lit metropolis, a detective hunts a rogue android that believes it can dream.',
    genre: 'Cyberpunk',
    tags: ['AI', 'Noir'],
    videoUrl: '#',
    hint: 'neon metropolis',
  },
  {
    id: '3',
    title: 'The Last Seed',
    thumbnailUrl: 'https://placehold.co/600x400.png',
    description: 'On a barren Earth, a young botanist guards the last living plant, hoping to find a place where it can grow.',
    genre: 'Post-Apocalyptic',
    tags: ['Hope', 'Survival'],
    videoUrl: '#',
    hint: 'barren earth',
  },
  {
    id: '4',
    title: 'Whispers of the Void',
    thumbnailUrl: 'https://placehold.co/600x400.png',
    description: 'An astronaut on a solo mission begins to receive cryptic messages from an unknown entity in deep space.',
    genre: 'Cosmic Horror',
    tags: ['Space', 'Mystery'],
    videoUrl: '#',
    hint: 'astronaut space',
  },
  {
    id: '5',
    title: 'Automated Heart',
    thumbnailUrl: 'https://placehold.co/600x400.png',
    description: 'A lonely engineer builds a companion robot, only to find it developing emotions far beyond its programming.',
    genre: 'Romance',
    tags: ['AI', 'Drama'],
    videoUrl: '#',
    hint: 'companion robot',
  },
  {
    id: '6',
    title: 'Nexus Point',
    thumbnailUrl: 'https://placehold.co/600x400.png',
    description: 'Multiple realities begin to collide, and a small group of individuals holds the key to preventing total collapse.',
    genre: 'Sci-Fi',
    tags: ['Multiverse', 'Action'],
    videoUrl: '#',
    hint: 'multiple realities',
  },
  {
    id: '7',
    title: 'Forgotten Fable',
    thumbnailUrl: 'https://placehold.co/600x400.png',
    description: 'An AI rediscovers a lost human fairytale and attempts to recreate its magic in a world that has forgotten wonder.',
    genre: 'Fantasy',
    tags: ['Magic', 'Adventure'],
    videoUrl: '#',
    hint: 'fantasy magic',
  },
  {
    id: '8',
    title: 'The Gilded Cage',
    thumbnailUrl: 'https://placehold.co/600x400.png',
    description: 'In a utopian society where all needs are met by AI, a citizen yearns for the chaos and freedom of the old world.',
    genre: 'Dystopian',
    tags: ['Utopia', 'Rebellion'],
    videoUrl: '#',
    hint: 'utopian society',
  },
];
