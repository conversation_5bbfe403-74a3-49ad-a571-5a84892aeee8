'use client';

import { useState, useMemo, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { FilmCard } from "@/components/FilmCard";
import { films } from "@/lib/data";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search } from 'lucide-react';

export default function FilmsPage() {
  const searchParams = useSearchParams();
  const genreFromQuery = searchParams.get('genre');

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedGenre, setSelectedGenre] = useState('all');

  const genres = useMemo(() => {
    const allGenres = films.map(film => film.genre);
    return ['all', ...Array.from(new Set(allGenres))];
  }, []);

  useEffect(() => {
    if (genreFromQuery && genres.includes(genreFromQuery)) {
      setSelectedGenre(genreFromQuery);
    }
  }, [genreFromQuery, genres]);

  const filteredFilms = useMemo(() => {
    return films.filter(film => {
      const matchesGenre = selectedGenre === 'all' || film.genre === selectedGenre;
      const matchesSearch = 
        film.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        film.description.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesGenre && matchesSearch;
    });
  }, [searchQuery, selectedGenre]);

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="mb-8 flex flex-col md:flex-row gap-4">
        <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
                type="text"
                placeholder="Search by title or description..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full"
            />
        </div>
        <Select value={selectedGenre} onValueChange={setSelectedGenre}>
          <SelectTrigger className="w-full md:w-[200px]">
            <SelectValue placeholder="Filter by genre" />
          </SelectTrigger>
          <SelectContent>
            {genres.map(genre => (
              <SelectItem key={genre} value={genre} className="capitalize">
                {genre === 'all' ? 'All Genres' : genre}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {filteredFilms.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {filteredFilms.map((film) => (
            <FilmCard key={film.id} film={film} />
          ))}
        </div>
      ) : (
        <div className="text-center py-16">
          <p className="text-xl text-muted-foreground">No films found matching your criteria.</p>
        </div>
      )}
    </div>
  );
}
