const fs = require("fs").promises;

async function checkYouTubeURLs() {
  const workingLinks = [];

  try {
    // Read the JSON file using promises
    const data = await fs.readFile("data/output.json", "utf8");
    const jsonArray = JSON.parse(data);

    for (const json of jsonArray) {
      const url = json.src;
      try {
        // Method 1: Try with more realistic headers
        const response = await fetch(url, {
          method: "GET",
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            Accept:
              "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            Connection: "keep-alive",
            "Upgrade-Insecure-Requests": "1",
          },
        });

        console.log(`Response status for ${url}: ${response.status}`);
        console.log(
          `Response headers:`,
          Object.fromEntries(response.headers.entries())
        );

        if (response.ok) {
          const text = await response.text();
          console.log(`Response text length: ${text.length}`);

          // If we get empty text, try alternative approach
          if (text.length === 0) {
            console.log(`⚠️  Empty response, trying YouTube embed check...`);

            // Method 2: Try checking the embed URL
            const videoId = url.match(
              /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/
            )?.[1];
            if (videoId) {
              const embedUrl = `https://www.youtube.com/embed/${videoId}`;
              const embedResponse = await fetch(embedUrl, {
                method: "HEAD",
                headers: {
                  "User-Agent":
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                },
              });

              console.log(`Embed response status: ${embedResponse.status}`);

              if (embedResponse.status === 200) {
                console.log(
                  `✅ Video appears to be available (embed accessible): ${url}`
                );
                workingLinks.push(json);
              } else {
                console.log(
                  `❌ Video may be unavailable (embed status ${embedResponse.status}): ${url}`
                );
              }
            }
            continue;
          }

          // Check for various YouTube unavailability messages
          const unavailabilityIndicators = [
            "This video isn't available anymore",
            "This video is unavailable",
            "Video unavailable",
            "This video has been removed",
            "This video is private",
            'playabilityStatus":"ERROR"',
            'playabilityStatus":"UNPLAYABLE"',
            '"status":"ERROR"',
            '"simpleText":"Video unavailable"',
            '"reason":{"simpleText":"This video is unavailable"}',
            'id="unavailable-message"',
          ];

          const isNotAvailable = unavailabilityIndicators.some((indicator) =>
            text.toLowerCase().includes(indicator.toLowerCase())
          );

          if (isNotAvailable) {
            console.log(`❌ Video is not available: ${url}`);
            console.log(`   Title: ${json.title}`);
          } else {
            // Additional check: look for positive indicators
            const availabilityIndicators = [
              '"videoDetails":',
              '"title":"',
              "ytInitialPlayerResponse",
              "var ytInitialData",
            ];

            const hasPositiveIndicators = availabilityIndicators.some(
              (indicator) => text.includes(indicator)
            );

            if (hasPositiveIndicators) {
              console.log(`✅ Video is available: ${url}`);
              console.log(`   Title: ${json.title}`);
              workingLinks.push(json);
            } else {
              console.log(`⚠️  Unclear status for: ${url}`);
              console.log(`   Title: ${json.title}`);
              // Assume working if no clear error indicators
              workingLinks.push(json);
            }
          }
        } else {
          console.log(`❌ URL returned status code ${response.status}: ${url}`);
          console.log(`   Title: ${json.title}`);
        }
      } catch (error) {
        console.log(`❌ URL is not working: ${url} - Error: ${error.message}`);
        console.log(`   Title: ${json.title}`);
      }

      // Add a longer delay to avoid rate limiting
      console.log("Waiting 3 seconds before next request...");
      await new Promise((resolve) => setTimeout(resolve, 3000));
    }

    // Save working links to working.json
    await fs.writeFile("working.json", JSON.stringify(workingLinks, null, 2));
    console.log(`\n📁 Working links saved to working.json`);
    console.log(
      `📊 Found ${workingLinks.length} working links out of ${jsonArray.length} total`
    );
  } catch (error) {
    console.error("Error:", error.message);
  }
}

// Call the async function
checkYouTubeURLs();
